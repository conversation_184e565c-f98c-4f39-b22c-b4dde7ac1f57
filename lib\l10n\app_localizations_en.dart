// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'FreeLancer Mobile';

  @override
  String get welcome => 'Welcome';

  @override
  String get login => 'Login';

  @override
  String get logout => 'Logout';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get settings => 'Settings';

  @override
  String get projects => 'Projects';

  @override
  String get clients => 'Clients';

  @override
  String get payments => 'Payments';

  @override
  String get invoices => 'Invoices';

  @override
  String get expenses => 'Expenses';

  @override
  String get menu => 'Menu';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get newProject => 'New Project';

  @override
  String get addClient => 'Add Client';

  @override
  String get recordPayment => 'Record Payment';

  @override
  String get createInvoice => 'Create Invoice';

  @override
  String get recentActivities => 'Recent Activities';

  @override
  String get upcomingDeadlines => 'Upcoming Deadlines';

  @override
  String get viewAll => 'View All';

  @override
  String get noRecentActivities => 'No recent activities';

  @override
  String get noUpcomingDeadlines => 'No upcoming deadlines';

  @override
  String get totalRevenue => 'Total Revenue';

  @override
  String get activeProjects => 'Active Projects';

  @override
  String get pendingPayments => 'Pending Payments';

  @override
  String get thisMonth => 'This Month';

  @override
  String get language => 'Language';

  @override
  String get theme => 'Theme';

  @override
  String get currency => 'Currency';

  @override
  String get notifications => 'Notifications';

  @override
  String get security => 'Security';

  @override
  String get about => 'About';

  @override
  String get cancel => 'Cancel';

  @override
  String get save => 'Save';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get add => 'Add';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get sort => 'Sort';

  @override
  String get refresh => 'Refresh';

  @override
  String get account => 'Account';

  @override
  String get profileSettings => 'Profile Settings';

  @override
  String get updatePersonalInfo => 'Update your personal information';

  @override
  String get changePassword => 'Change Password';

  @override
  String get updateAccountPassword => 'Update your account password';

  @override
  String get emailSettings => 'Email Settings';

  @override
  String get manageEmailPreferences => 'Manage email preferences';

  @override
  String get application => 'Application';

  @override
  String get appearance => 'Appearance';

  @override
  String get themeDisplaySettings => 'Theme and display settings';

  @override
  String get dataStorage => 'Data & Storage';

  @override
  String get backupSyncSettings => 'Backup and sync settings';

  @override
  String get business => 'Business';

  @override
  String get businessProfile => 'Business Profile';

  @override
  String get companyInfoBranding => 'Company information and branding';

  @override
  String get invoiceSettings => 'Invoice Settings';

  @override
  String get defaultInvoiceTemplates =>
      'Default invoice templates and settings';

  @override
  String get taxSettings => 'Tax Settings';

  @override
  String get taxRatesCalculation => 'Tax rates and calculation preferences';

  @override
  String get currencyRates => 'Currency & Rates';

  @override
  String get supportInformation => 'Support & Information';

  @override
  String get helpSupport => 'Help & Support';

  @override
  String get getHelpContactSupport => 'Get help and contact support';

  @override
  String get termsPrivacy => 'Terms & Privacy';

  @override
  String get termsServicePrivacyPolicy => 'Terms of service and privacy policy';

  @override
  String get appVersionInfo => 'App version and information';

  @override
  String get userName => 'User Name';

  @override
  String get userEmail => '<EMAIL>';

  @override
  String get freelancerAccount => 'Freelancer Account';

  @override
  String get signOut => 'Sign Out';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get autoSync => 'Auto Sync';

  @override
  String get autoSyncDescription => 'Automatically sync data when online';

  @override
  String get databaseBackupRestore => 'Database Backup & Restore';

  @override
  String get backup => 'Backup';

  @override
  String get restore => 'Restore';

  @override
  String get settingsExportImport => 'Settings Export & Import';

  @override
  String get export => 'Export';

  @override
  String get import => 'Import';

  @override
  String get resetDatabase => 'Reset Database';

  @override
  String get close => 'Close';

  @override
  String get defaultCurrency => 'Default Currency';

  @override
  String get importSettings => 'Import Settings';

  @override
  String get pasteSettingsJson => 'Paste settings JSON here';

  @override
  String get restoreDatabase => 'Restore Database';

  @override
  String get restoreCancelled => 'Restore cancelled';

  @override
  String get welcomeBack => 'Welcome Back';

  @override
  String get signInToContinue => 'Sign in to continue';

  @override
  String get createAccount => 'Create Account';

  @override
  String get joinOurCommunity => 'Join our community';

  @override
  String get fullName => 'Full Name';

  @override
  String get enterFullName => 'Enter your full name';

  @override
  String get email => 'Email';

  @override
  String get enterEmail => 'Enter your email';

  @override
  String get password => 'Password';

  @override
  String get enterPassword => 'Enter your password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get confirmPasswordPlaceholder => 'Confirm your password';

  @override
  String get signUp => 'Sign Up';

  @override
  String get dontHaveAccount => 'Don\'t have an account? ';

  @override
  String get alreadyHaveAccount => 'Already have an account? ';

  @override
  String get signIn => 'Sign In';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Information';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get confirm => 'Confirm';

  @override
  String get retry => 'Retry';

  @override
  String get continueAction => 'Continue';

  @override
  String get back => 'Back';

  @override
  String get next => 'Next';

  @override
  String get done => 'Done';

  @override
  String get apply => 'Apply';

  @override
  String get clear => 'Clear';

  @override
  String get reset => 'Reset';

  @override
  String get submit => 'Submit';

  @override
  String get send => 'Send';

  @override
  String get update => 'Update';

  @override
  String get create => 'Create';

  @override
  String get remove => 'Remove';

  @override
  String get select => 'Select';

  @override
  String get choose => 'Choose';

  @override
  String get browse => 'Browse';

  @override
  String get upload => 'Upload';

  @override
  String get download => 'Download';

  @override
  String get share => 'Share';

  @override
  String get copy => 'Copy';

  @override
  String get paste => 'Paste';

  @override
  String get cut => 'Cut';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get noActivitiesYet => 'No activities yet';

  @override
  String get noActivitiesMatchFilters => 'No activities match your filters';

  @override
  String get tryAdjustingFilters => 'Try adjusting your search or filters';

  @override
  String get activitiesWillAppear =>
      'Activities will appear here as you use the app';

  @override
  String get noDeadlinesFound => 'No deadlines found';

  @override
  String get addProjectsInvoices =>
      'Add projects and invoices to track deadlines';

  @override
  String get demo => 'Demo';

  @override
  String get showingDemoDeadlines =>
      'Showing demo deadlines. Add real projects and invoices to track actual deadlines.';

  @override
  String get reports => 'Reports';

  @override
  String get filters => 'Filters';

  @override
  String get timePeriod => 'Time Period';

  @override
  String get advancedFilters => 'Advanced Filters';

  @override
  String get client => 'Client';

  @override
  String get project => 'Project';

  @override
  String get status => 'Status';

  @override
  String get amount => 'Amount';

  @override
  String get min => 'Min';

  @override
  String get max => 'Max';

  @override
  String get all => 'All';

  @override
  String get completed => 'Completed';

  @override
  String get inProgress => 'In Progress';

  @override
  String get notStarted => 'Not Started';

  @override
  String get pending => 'Pending';

  @override
  String get customDateRange => 'Custom Date Range';

  @override
  String get selectDateRange => 'Select Date Range';

  @override
  String get markAsPaid => 'Mark as Paid';

  @override
  String get createPayment => 'Create Payment';

  @override
  String get paymentDetails => 'Payment Details:';

  @override
  String get creatingPayment => 'Creating payment...';

  @override
  String get paymentCreatedSuccessfully => 'Payment created successfully!';

  @override
  String get errorCreatingPayment => 'Error creating payment';

  @override
  String get cannotMarkPaid =>
      'Cannot mark project as paid: Missing project or client information';

  @override
  String get projectFullyPaid => 'Project is already fully paid';

  @override
  String get createPaymentForRemaining =>
      'Create a payment for the remaining amount?';

  @override
  String get languageChanged => 'Language changed to';

  @override
  String get failedToLoadDashboard => 'Failed to load dashboard';

  @override
  String get errorLoadingDashboard => 'Error loading dashboard';

  @override
  String get errorRefreshingDashboard => 'Error refreshing dashboard';

  @override
  String get showingDemoData =>
      'Showing demo data. Start adding projects, payments, and invoices to see real activities.';

  @override
  String get thisWeek => 'This Week';

  @override
  String get lastWeek => 'Last Week';

  @override
  String get last30Days => 'Last 30 Days';

  @override
  String get last90Days => 'Last 90 Days';

  @override
  String get thisYear => 'This Year';

  @override
  String get lastYear => 'Last Year';

  @override
  String get totalEarnings => 'Total Earnings';

  @override
  String get completedProjects => 'Completed Projects';

  @override
  String get unpaidProjects => 'Unpaid Projects';

  @override
  String get monthlyPerformance => 'Monthly Performance';

  @override
  String get yearlyPerformance => 'Yearly Performance';

  @override
  String get lastUpdated => 'Last updated';

  @override
  String get refreshDashboard => 'Refresh Dashboard';

  @override
  String get profit => 'Profit';

  @override
  String get revenue => 'Revenue';

  @override
  String get active => 'Active';

  @override
  String get netIncome => 'Net Income';

  @override
  String get justNow => 'Just now';

  @override
  String minutesAgo(Object minutes) {
    return '${minutes}m ago';
  }

  @override
  String hoursAgo(Object hours) {
    return '${hours}h ago';
  }

  @override
  String daysAgo(Object days) {
    return '${days}d ago';
  }

  @override
  String currencyMillion(Object amount) {
    return '${amount}M DA';
  }

  @override
  String currencyThousand(Object amount) {
    return '${amount}K DA';
  }

  @override
  String currencyPlain(Object amount) {
    return '$amount DA';
  }

  @override
  String overduePayment(Object days) {
    return 'Overdue $days days';
  }

  @override
  String dueInDays(Object days) {
    return 'Due in $days days';
  }

  @override
  String get taxManagement => 'Tax Management';

  @override
  String get taxYear => 'Tax Year:';

  @override
  String get taxStatistics => 'Tax Statistics';

  @override
  String get paid => 'Paid';

  @override
  String get overdue => 'Overdue';

  @override
  String get alerts => 'Alerts';

  @override
  String get calculateTaxesForYear => 'Calculate your taxes for this year';

  @override
  String get calculateTaxes => 'Calculate Taxes';

  @override
  String taxPaymentsForYear(int year) {
    return 'Tax Payments $year';
  }

  @override
  String noTaxesCalculated(int year) {
    return 'No taxes calculated for $year';
  }

  @override
  String dueDate(Object day, Object month, Object year) {
    return 'Due: $day/$month/$year';
  }

  @override
  String currencyWithSymbol(Object amount) {
    return '$amount DA';
  }

  @override
  String get dueToday => 'Due today';

  @override
  String get dueTomorrow => 'Due tomorrow';

  @override
  String overdueBy(int days) {
    String _temp0 = intl.Intl.pluralLogic(
      days,
      locale: localeName,
      other: '# days',
      one: '# day',
    );
    return 'Overdue by $_temp0';
  }

  @override
  String dueIn(int days) {
    String _temp0 = intl.Intl.pluralLogic(
      days,
      locale: localeName,
      other: '$days days',
      one: '$days day',
      zero: 'today',
    );
    return 'Due in $_temp0';
  }

  @override
  String get businessManagement => 'Business Management';

  @override
  String get manageAllBusiness =>
      'Manage all aspects of your freelance business';

  @override
  String get projectManagement => 'Project Management';

  @override
  String get manageProjectsTrackProgress =>
      'Manage your projects and track progress';

  @override
  String get viewProjects => 'View Projects';

  @override
  String get addProject => 'Add Project';

  @override
  String get manageClients => 'Client Management';

  @override
  String get trackPayments => 'Payment Tracking';

  @override
  String get manageExpenses => 'Expense Management';

  @override
  String get createInvoices => 'Invoice Creation';

  @override
  String get taxes => 'Taxes';

  @override
  String get calendar => 'Calendar';

  @override
  String get viewEvents => 'View Events';

  @override
  String get businessAnalytics => 'Business Analytics';

  @override
  String get allDeadlines => 'All Deadlines';

  @override
  String noDeadlinesWithFilter(String filter) {
    return 'No $filter deadlines found';
  }

  @override
  String get appInformation => 'App Information';

  @override
  String get version => 'Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get releaseDate => 'Release Date';

  @override
  String get platform => 'Platform';

  @override
  String get framework => 'Framework';

  @override
  String get database => 'Database';

  @override
  String get developer => 'Developer';

  @override
  String get developerName => 'Freelancer Mobile Team';

  @override
  String get developerDescription => 'Specialized in mobile app development';

  @override
  String get appDescription =>
      'Designed specifically for Algerian freelancers to manage their business efficiently with local tax compliance and Arabic language support.';

  @override
  String get keyFeatures => 'Key Features';

  @override
  String get clientManagement => 'Client Management';

  @override
  String get paymentTracking => 'Payment Tracking';

  @override
  String get expenseManagement => 'Expense Management';

  @override
  String get invoiceGeneration => 'Invoice Generation';

  @override
  String get algerianTaxManagement => 'Algerian Tax Management';

  @override
  String get calendarEvents => 'Calendar & Events';

  @override
  String get businessReports => 'Business Reports';

  @override
  String get smartNotifications => 'Smart Notifications';

  @override
  String get legal => 'Legal';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get termsOfServiceDescription => 'Read our terms and conditions';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get privacyPolicyDescription => 'How we protect your data';

  @override
  String get openSourceLicenses => 'Open Source Licenses';

  @override
  String get openSourceLicensesDescription =>
      'Third-party libraries and licenses';

  @override
  String get contactSupport => 'Contact & Support';

  @override
  String get emailSupport => 'Email Support';

  @override
  String get website => 'Website';

  @override
  String get rateUs => 'Rate Us';

  @override
  String get reportBug => 'Report Bug';

  @override
  String get rateUsDescription => 'Rate the app on Play Store';

  @override
  String get reportBugDescription => 'Help us improve the app';

  @override
  String get invoice => 'Invoice';

  @override
  String get tax => 'Tax';

  @override
  String get payment => 'Payment';

  @override
  String get appPreferences => 'App preferences';

  @override
  String get editProject => 'Edit Project';

  @override
  String get projectName => 'Project Name';

  @override
  String get enterProjectName => 'Enter a descriptive project name';

  @override
  String get description => 'Description';

  @override
  String get enterDescription => 'Describe what this project involves...';

  @override
  String get selectClient => 'Select Client';

  @override
  String get noClientsFound => 'No Clients Found';

  @override
  String get pleaseSelectClient => 'Please select a client';

  @override
  String get pricingDetails => 'Pricing Details';

  @override
  String get fixedPrice => 'Fixed Price';

  @override
  String get hourlyRate => 'Hourly Rate';

  @override
  String get estimatedHours => 'Estimated Hours';

  @override
  String get actualHours => 'Actual Hours';

  @override
  String get timelineStatus => 'Timeline & Status';

  @override
  String get projectStatus => 'Project Status';

  @override
  String get projectTimeline => 'Project Timeline';

  @override
  String get progress => 'Progress (%)';

  @override
  String get startDate => 'Start Date';

  @override
  String get endDate => 'End Date';

  @override
  String get previous => 'Previous';

  @override
  String get createProject => 'Create Project';

  @override
  String get updateProject => 'Update Project';

  @override
  String get enterFixedAmountError =>
      'Please enter fixed amount for fixed price projects';

  @override
  String get fixedAmountGreaterThanZero =>
      'Fixed amount must be greater than 0';

  @override
  String get enterHourlyRateError =>
      'Please enter hourly rate for hourly rate projects';

  @override
  String get hourlyRateGreaterThanZero => 'Hourly rate must be greater than 0';

  @override
  String get freelanceManagementSlogan =>
      'Complete Freelance Management Solution';

  @override
  String get appInfo => 'App Information';

  @override
  String get endDateAfterStart => 'End date must be after start date';

  @override
  String get progressBetween0And100 =>
      'Progress percentage must be between 0 and 100';

  @override
  String get validProgressRequired =>
      'Please enter a valid progress percentage';

  @override
  String get projectUpdated => 'Project updated successfully';

  @override
  String get projectCreated => 'Project created successfully';

  @override
  String projectSaveError(Object error) {
    return 'Error saving project: $error';
  }

  @override
  String get basicInfo => 'Basic Info';

  @override
  String get pricing => 'Pricing';

  @override
  String get timeline => 'Timeline';

  @override
  String get projectInfo => 'Project Information';

  @override
  String get projectDetailsHint => 'Enter basic details about your project';

  @override
  String get projectNameHint => 'Enter a descriptive project name';

  @override
  String get descriptionHint => 'Describe what this project involves...';

  @override
  String get tipProjectDescription =>
      'Tip: Use a clear, descriptive name and detailed description to help track your project progress.';

  @override
  String get clientSelectionHint => 'Choose the client for this project';

  @override
  String get selectClientHint => 'Select a client for this project';

  @override
  String get clientSelected => 'Client Selected';

  @override
  String clientCurrencyInfo(Object currency) {
    return 'Currency will be set to $currency';
  }

  @override
  String get addClientInfo =>
      'You need to add at least one client before creating a project. Go to Client Management to add clients.';

  @override
  String get pricingHint => 'Set your pricing model and rates';

  @override
  String get pricingModel => 'Pricing Model';

  @override
  String get oneTimePayment => 'One-time payment';

  @override
  String get payPerHour => 'Pay per hour';

  @override
  String get fixedAmount => 'Fixed Amount';

  @override
  String get totalProjectAmount => 'Enter total project amount';

  @override
  String get hourlyRateHint => 'Enter your hourly rate';

  @override
  String get estimatedHoursHint => 'Est. hours';

  @override
  String get actualHoursHint => 'Actual hours';

  @override
  String get timelineHint => 'Set project timeline and current status';

  @override
  String get progressHint => 'Enter progress percentage (0-100)';

  @override
  String get startDateHint => 'Select start date';

  @override
  String get endDateHint => 'Select end date';

  @override
  String get readyToCreate => 'Ready to Create Project';

  @override
  String get reviewCreateInfo =>
      'Review all the information and click \"Create Project\" to add this project to your portfolio.';

  @override
  String get updating => 'Updating...';

  @override
  String get creating => 'Creating...';

  @override
  String errorLoadingClients(Object error) {
    return 'Error loading clients: $error';
  }

  @override
  String get clientNotFoundWarning =>
      'Warning: Original client not found. Please select a client.';

  @override
  String get projectNameRequired => 'Project name is required';

  @override
  String get descriptionRequired => 'Description is required';

  @override
  String get hourlyRateRequired => 'Hourly rate is required';

  @override
  String get enterValidHourlyRate => 'Please enter a valid hourly rate';

  @override
  String get fixedAmountRequired => 'Fixed amount is required';

  @override
  String get enterValidAmount => 'Please enter a valid amount';

  @override
  String get enterValidHours => 'Please enter valid hours';

  @override
  String get paymentFilterFullyPaid => 'Fully Paid';

  @override
  String get paymentFilterPartiallyPaid => 'Partially Paid';

  @override
  String get paymentFilterUnpaid => 'Unpaid';

  @override
  String get paymentFilterOverdue => 'Overdue';

  @override
  String get noProjectsFound => 'No projects found';

  @override
  String get noProjectsYet => 'No projects yet';

  @override
  String get adjustSearchOrFilters => 'Try adjusting your search or filters';

  @override
  String get createFirstProject => 'Create your first project to get started';

  @override
  String get getStarted => 'Get Started';

  @override
  String get skip => 'Skip';

  @override
  String get welcomeSubtitle => 'Manage your freelance business with ease';
}
