import 'package:flutter/material.dart';

class AppColors {
  // Primary colors - Simple black theme
  static const Color primary = Color(0xFF000000);
  static const Color primaryLight = Color(0xFF333333);
  static const Color primaryDark = Color(0xFF000000);

  // Background colors - Clean and minimal
  static const Color background = Color(0xFFFFFFFF);
  static const Color surface = Color(0xFFF8F8F8);
  static const Color surfaceDark = Color(0xFF000000);
  static const Color cardBackground = Color(0xFFFFFFFF);

  // Accent colors
  static const Color accent = Color(0xFF666666);

  // Text colors - High contrast
  static const Color textPrimary = Color(0xFF000000);
  static const Color textSecondary = Color(0xFF666666);
  static const Color textLight = Color(0xFF999999);
  static const Color textWhite = Color(0xFFFFFFFF);

  // Status colors - Minimal
  static const Color success = Color(0xFF000000);
  static const Color error = Color(0xFF000000);
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderFocus = Color(0xFF000000);

  // Additional colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color shadow = Color(0xFF000000);
}

