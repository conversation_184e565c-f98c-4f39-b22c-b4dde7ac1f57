// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'FreeLancer Mobile';

  @override
  String get welcome => 'Bienvenue';

  @override
  String get login => 'Connexion';

  @override
  String get logout => 'Déconnexion';

  @override
  String get dashboard => 'Tableau de bord';

  @override
  String get settings => 'Paramètres';

  @override
  String get projects => 'Projets';

  @override
  String get clients => 'Clients';

  @override
  String get payments => 'Paiements';

  @override
  String get invoices => 'Factures';

  @override
  String get expenses => 'Dépenses';

  @override
  String get menu => 'Menu';

  @override
  String get quickActions => 'Actions rapides';

  @override
  String get newProject => 'Nouveau projet';

  @override
  String get addClient => 'Ajouter un client';

  @override
  String get recordPayment => 'Enregistrer un paiement';

  @override
  String get createInvoice => 'Créer une facture';

  @override
  String get recentActivities => 'Activités récentes';

  @override
  String get upcomingDeadlines => 'Échéances à venir';

  @override
  String get viewAll => 'Voir tout';

  @override
  String get noRecentActivities => 'Aucune activité récente';

  @override
  String get noUpcomingDeadlines => 'Aucune échéance à venir';

  @override
  String get totalRevenue => 'Chiffre d\'affaires total';

  @override
  String get activeProjects => 'Projets actifs';

  @override
  String get pendingPayments => 'Paiements en attente';

  @override
  String get thisMonth => 'Ce mois-ci';

  @override
  String get language => 'Langue';

  @override
  String get theme => 'Thème';

  @override
  String get currency => 'Devise';

  @override
  String get notifications => 'Notifications';

  @override
  String get security => 'Sécurité';

  @override
  String get about => 'À propos';

  @override
  String get cancel => 'Annuler';

  @override
  String get save => 'Enregistrer';

  @override
  String get edit => 'Modifier';

  @override
  String get delete => 'Supprimer';

  @override
  String get add => 'Ajouter';

  @override
  String get search => 'Rechercher';

  @override
  String get filter => 'Filtrer';

  @override
  String get sort => 'Trier';

  @override
  String get refresh => 'Actualiser';

  @override
  String get account => 'Compte';

  @override
  String get profileSettings => 'Paramètres du profil';

  @override
  String get updatePersonalInfo =>
      'Mettre à jour vos informations personnelles';

  @override
  String get changePassword => 'Changer le mot de passe';

  @override
  String get updateAccountPassword =>
      'Mettre à jour le mot de passe de votre compte';

  @override
  String get emailSettings => 'Paramètres e-mail';

  @override
  String get manageEmailPreferences => 'Gérer les préférences e-mail';

  @override
  String get application => 'Application';

  @override
  String get appearance => 'Apparence';

  @override
  String get themeDisplaySettings => 'Paramètres de thème et d\'affichage';

  @override
  String get dataStorage => 'Données et stockage';

  @override
  String get backupSyncSettings =>
      'Paramètres de sauvegarde et synchronisation';

  @override
  String get business => 'Entreprise';

  @override
  String get businessProfile => 'Profil d\'entreprise';

  @override
  String get companyInfoBranding =>
      'Informations de l\'entreprise et image de marque';

  @override
  String get invoiceSettings => 'Paramètres de facturation';

  @override
  String get defaultInvoiceTemplates =>
      'Modèles de factures et paramètres par défaut';

  @override
  String get taxSettings => 'Paramètres fiscaux';

  @override
  String get taxRatesCalculation =>
      'Taux d\'imposition et préférences de calcul';

  @override
  String get currencyRates => 'Devises et taux';

  @override
  String get supportInformation => 'Support et informations';

  @override
  String get helpSupport => 'Aide et support';

  @override
  String get getHelpContactSupport =>
      'Obtenir de l\'aide et contacter le support';

  @override
  String get termsPrivacy => 'Conditions et confidentialité';

  @override
  String get termsServicePrivacyPolicy =>
      'Conditions d\'utilisation et politique de confidentialité';

  @override
  String get appVersionInfo => 'Version de l\'application et informations';

  @override
  String get userName => 'Nom d\'utilisateur';

  @override
  String get userEmail => '<EMAIL>';

  @override
  String get freelancerAccount => 'Compte freelance';

  @override
  String get signOut => 'Se déconnecter';

  @override
  String get selectLanguage => 'Sélectionner la langue';

  @override
  String get autoSync => 'Synchronisation automatique';

  @override
  String get autoSyncDescription =>
      'Synchroniser automatiquement les données en ligne';

  @override
  String get databaseBackupRestore =>
      'Sauvegarde et restauration de la base de données';

  @override
  String get backup => 'Sauvegarder';

  @override
  String get restore => 'Restaurer';

  @override
  String get settingsExportImport => 'Export et import des paramètres';

  @override
  String get export => 'Exporter';

  @override
  String get import => 'Importer';

  @override
  String get resetDatabase => 'Réinitialiser la base de données';

  @override
  String get close => 'Fermer';

  @override
  String get defaultCurrency => 'Devise par défaut';

  @override
  String get importSettings => 'Importer les paramètres';

  @override
  String get pasteSettingsJson => 'Coller le JSON des paramètres ici';

  @override
  String get restoreDatabase => 'Restaurer la base de données';

  @override
  String get restoreCancelled => 'Restauration annulée';

  @override
  String get welcomeBack => 'Bon retour';

  @override
  String get signInToContinue => 'Connectez-vous pour continuer';

  @override
  String get createAccount => 'Créer un compte';

  @override
  String get joinOurCommunity => 'Rejoignez notre communauté';

  @override
  String get fullName => 'Nom complet';

  @override
  String get enterFullName => 'Entrez votre nom complet';

  @override
  String get email => 'E-mail';

  @override
  String get enterEmail => 'Entrez votre e-mail';

  @override
  String get password => 'Mot de passe';

  @override
  String get enterPassword => 'Entrez votre mot de passe';

  @override
  String get confirmPassword => 'Confirmer le mot de passe';

  @override
  String get confirmPasswordPlaceholder => 'Confirmez votre mot de passe';

  @override
  String get signUp => 'S\'inscrire';

  @override
  String get dontHaveAccount => 'Vous n\'avez pas de compte ? ';

  @override
  String get alreadyHaveAccount => 'Vous avez déjà un compte ? ';

  @override
  String get signIn => 'Se connecter';

  @override
  String get loading => 'Chargement...';

  @override
  String get error => 'Erreur';

  @override
  String get success => 'Succès';

  @override
  String get warning => 'Avertissement';

  @override
  String get info => 'Information';

  @override
  String get ok => 'OK';

  @override
  String get yes => 'Oui';

  @override
  String get no => 'Non';

  @override
  String get confirm => 'Confirmer';

  @override
  String get retry => 'Réessayer';

  @override
  String get continueAction => 'Continuer';

  @override
  String get back => 'Retour';

  @override
  String get next => 'Suivant';

  @override
  String get done => 'Terminé';

  @override
  String get apply => 'Appliquer';

  @override
  String get clear => 'Effacer';

  @override
  String get reset => 'Réinitialiser';

  @override
  String get submit => 'Soumettre';

  @override
  String get send => 'Envoyer';

  @override
  String get update => 'Mettre à jour';

  @override
  String get create => 'Créer';

  @override
  String get remove => 'Supprimer';

  @override
  String get select => 'Sélectionner';

  @override
  String get choose => 'Choisir';

  @override
  String get browse => 'Parcourir';

  @override
  String get upload => 'Télécharger';

  @override
  String get download => 'Télécharger';

  @override
  String get share => 'Partager';

  @override
  String get copy => 'Copier';

  @override
  String get paste => 'Coller';

  @override
  String get cut => 'Couper';

  @override
  String get undo => 'Annuler';

  @override
  String get redo => 'Rétablir';

  @override
  String get noActivitiesYet => 'Aucune activité pour le moment';

  @override
  String get noActivitiesMatchFilters =>
      'Aucune activité ne correspond à vos filtres';

  @override
  String get tryAdjustingFilters =>
      'Essayez d\'ajuster votre recherche ou vos filtres';

  @override
  String get activitiesWillAppear =>
      'Les activités apparaîtront ici au fur et à mesure que vous utilisez l\'application';

  @override
  String get noDeadlinesFound => 'Aucune échéance trouvée';

  @override
  String get addProjectsInvoices =>
      'Ajoutez des projets et des factures pour suivre les échéances';

  @override
  String get demo => 'Démo';

  @override
  String get showingDemoDeadlines =>
      'Affichage des échéances de démonstration. Ajoutez de vrais projets et factures pour suivre les échéances réelles.';

  @override
  String get reports => 'Rapports';

  @override
  String get filters => 'Filtres';

  @override
  String get timePeriod => 'Période';

  @override
  String get advancedFilters => 'Filtres avancés';

  @override
  String get client => 'Client';

  @override
  String get project => 'Projet';

  @override
  String get status => 'Statut';

  @override
  String get amount => 'Montant';

  @override
  String get min => 'Min';

  @override
  String get max => 'Max';

  @override
  String get all => 'Tous';

  @override
  String get completed => 'Terminé';

  @override
  String get inProgress => 'En cours';

  @override
  String get notStarted => 'Non commencé';

  @override
  String get pending => 'En attente';

  @override
  String get customDateRange => 'Plage de dates personnalisée';

  @override
  String get selectDateRange => 'Sélectionner une plage de dates';

  @override
  String get markAsPaid => 'Marquer comme payé';

  @override
  String get createPayment => 'Créer un paiement';

  @override
  String get paymentDetails => 'Détails du paiement :';

  @override
  String get creatingPayment => 'Création du paiement...';

  @override
  String get paymentCreatedSuccessfully => 'Paiement créé avec succès !';

  @override
  String get errorCreatingPayment => 'Erreur lors de la création du paiement';

  @override
  String get cannotMarkPaid =>
      'Impossible de marquer le projet comme payé : Informations du projet ou du client manquantes';

  @override
  String get projectFullyPaid => 'Le projet est déjà entièrement payé';

  @override
  String get createPaymentForRemaining =>
      'Créer un paiement pour le montant restant ?';

  @override
  String get languageChanged => 'Langue changée en';

  @override
  String get failedToLoadDashboard => 'Échec du chargement du tableau de bord';

  @override
  String get errorLoadingDashboard =>
      'Erreur lors du chargement du tableau de bord';

  @override
  String get errorRefreshingDashboard =>
      'Erreur lors de l\'actualisation du tableau de bord';

  @override
  String get showingDemoData =>
      'Affichage des données de démonstration. Commencez à ajouter des projets, des paiements et des factures pour voir les vraies activités.';

  @override
  String get thisWeek => 'Cette semaine';

  @override
  String get lastWeek => 'La semaine dernière';

  @override
  String get last30Days => 'Les 30 derniers jours';

  @override
  String get last90Days => 'Les 90 derniers jours';

  @override
  String get thisYear => 'Cette année';

  @override
  String get lastYear => 'L\'année dernière';

  @override
  String get totalEarnings => 'Gains totaux';

  @override
  String get completedProjects => 'Projets terminés';

  @override
  String get unpaidProjects => 'Projets impayés';

  @override
  String get monthlyPerformance => 'Performance mensuelle';

  @override
  String get yearlyPerformance => 'Performance annuelle';

  @override
  String get lastUpdated => 'Dernière mise à jour';

  @override
  String get refreshDashboard => 'Actualiser le tableau de bord';

  @override
  String get profit => 'Profit';

  @override
  String get revenue => 'Revenu';

  @override
  String get active => 'Actif';

  @override
  String get netIncome => 'Revenu net';

  @override
  String get justNow => 'À l’instant';

  @override
  String minutesAgo(Object minutes) {
    return 'il y a $minutes min';
  }

  @override
  String hoursAgo(Object hours) {
    return 'il y a $hours h';
  }

  @override
  String daysAgo(Object days) {
    return 'il y a $days j';
  }

  @override
  String currencyMillion(Object amount) {
    return '$amount M DA';
  }

  @override
  String currencyThousand(Object amount) {
    return '$amount K DA';
  }

  @override
  String currencyPlain(Object amount) {
    return '$amount DA';
  }

  @override
  String overduePayment(Object days) {
    return 'En retard de $days jours';
  }

  @override
  String dueInDays(Object days) {
    return 'À payer dans $days jours';
  }

  @override
  String get taxManagement => 'Gestion des impôts';

  @override
  String get taxYear => 'Année fiscale :';

  @override
  String get taxStatistics => 'Statistiques fiscales';

  @override
  String get paid => 'Payé';

  @override
  String get overdue => 'En retard';

  @override
  String get alerts => 'Alertes';

  @override
  String get calculateTaxesForYear => 'Calculez vos impôts pour cette année';

  @override
  String get calculateTaxes => 'Calculer les impôts';

  @override
  String taxPaymentsForYear(int year) {
    return 'Paiements d\'impôts $year';
  }

  @override
  String noTaxesCalculated(int year) {
    return 'Aucun impôt calculé pour $year';
  }

  @override
  String dueDate(Object day, Object month, Object year) {
    return 'Échéance : $day/$month/$year';
  }

  @override
  String currencyWithSymbol(Object amount) {
    return '$amount DA';
  }

  @override
  String get dueToday => 'Échéance aujourd’hui';

  @override
  String get dueTomorrow => 'Échéance demain';

  @override
  String overdueBy(int days) {
    String _temp0 = intl.Intl.pluralLogic(
      days,
      locale: localeName,
      other: '# jours',
      one: '# jour',
    );
    return 'En retard de $_temp0';
  }

  @override
  String dueIn(int days) {
    String _temp0 = intl.Intl.pluralLogic(
      days,
      locale: localeName,
      other: '$days days',
      one: '$days day',
      zero: 'today',
    );
    return 'Due in $_temp0';
  }

  @override
  String get businessManagement => 'Gestion d\'entreprise';

  @override
  String get manageAllBusiness =>
      'Gérez tous les aspects de votre activité freelance';

  @override
  String get projectManagement => 'Gestion de projet';

  @override
  String get manageProjectsTrackProgress =>
      'Gérez vos projets et suivez leur progression';

  @override
  String get viewProjects => 'Voir les projets';

  @override
  String get addProject => 'Ajouter un projet';

  @override
  String get manageClients => 'Gérer les clients';

  @override
  String get trackPayments => 'Suivre les paiements';

  @override
  String get manageExpenses => 'Gérer les dépenses';

  @override
  String get createInvoices => 'Créer des factures';

  @override
  String get taxes => 'Impôts';

  @override
  String get calendar => 'Calendrier';

  @override
  String get viewEvents => 'Voir les événements';

  @override
  String get businessAnalytics => 'Analyse d\'entreprise';

  @override
  String get allDeadlines => 'Toutes les échéances';

  @override
  String noDeadlinesWithFilter(String filter) {
    return 'Aucune échéance pour $filter';
  }

  @override
  String get appInformation => 'Informations sur l\'application';

  @override
  String get version => 'Version';

  @override
  String get buildNumber => 'Numéro de build';

  @override
  String get releaseDate => 'Date de publication';

  @override
  String get platform => 'Plateforme';

  @override
  String get framework => 'Framework';

  @override
  String get database => 'Base de données';

  @override
  String get developer => 'Développeur';

  @override
  String get developerName => 'Équipe Freelancer Mobile';

  @override
  String get developerDescription =>
      'Spécialisée dans le développement d\'applications mobiles';

  @override
  String get appDescription =>
      'Conçue spécialement pour les freelances algériens afin de gérer efficacement leur activité avec conformité fiscale locale et prise en charge de la langue arabe.';

  @override
  String get keyFeatures => 'Fonctionnalités clés';

  @override
  String get clientManagement => 'Gestion des clients';

  @override
  String get paymentTracking => 'Suivi des paiements';

  @override
  String get expenseManagement => 'Gestion des dépenses';

  @override
  String get invoiceGeneration => 'Génération de factures';

  @override
  String get algerianTaxManagement => 'Gestion fiscale algérienne';

  @override
  String get calendarEvents => 'Calendrier et événements';

  @override
  String get businessReports => 'Rapports d\'activité';

  @override
  String get smartNotifications => 'Notifications intelligentes';

  @override
  String get legal => 'Mentions légales';

  @override
  String get termsOfService => 'Conditions d\'utilisation';

  @override
  String get termsOfServiceDescription => 'Lisez nos conditions générales';

  @override
  String get privacyPolicy => 'Politique de confidentialité';

  @override
  String get privacyPolicyDescription => 'Comment nous protégeons vos données';

  @override
  String get openSourceLicenses => 'Licences open source';

  @override
  String get openSourceLicensesDescription =>
      'Bibliothèques tierces et licences';

  @override
  String get contactSupport => 'Contact & Support';

  @override
  String get emailSupport => 'Support par email';

  @override
  String get website => 'Site web';

  @override
  String get rateUs => 'Noter l\'application';

  @override
  String get reportBug => 'Signaler un bug';

  @override
  String get rateUsDescription => 'Notez l’application sur le Play Store';

  @override
  String get reportBugDescription => 'Aidez-nous à améliorer l\'application';

  @override
  String get invoice => 'Facture';

  @override
  String get tax => 'Impôt';

  @override
  String get payment => 'Paiement';

  @override
  String get appPreferences => 'Préférences de l\'application';

  @override
  String get editProject => 'Modifier le projet';

  @override
  String get projectName => 'Nom du projet';

  @override
  String get enterProjectName => 'Entrez un nom descriptif';

  @override
  String get description => 'Description';

  @override
  String get enterDescription => 'Décrivez le contenu du projet...';

  @override
  String get selectClient => 'Sélectionner un client';

  @override
  String get noClientsFound => 'Aucun client trouvé';

  @override
  String get pleaseSelectClient => 'Veuillez sélectionner un client';

  @override
  String get pricingDetails => 'Détails de tarification';

  @override
  String get fixedPrice => 'Prix fixe';

  @override
  String get hourlyRate => 'Taux horaire';

  @override
  String get estimatedHours => 'Heures estimées';

  @override
  String get actualHours => 'Heures réelles';

  @override
  String get timelineStatus => 'Calendrier et statut';

  @override
  String get projectStatus => 'Statut du projet';

  @override
  String get projectTimeline => 'Calendrier du projet';

  @override
  String get progress => 'Avancement (%)';

  @override
  String get startDate => 'Date de début';

  @override
  String get endDate => 'Date de fin';

  @override
  String get previous => 'Précédent';

  @override
  String get createProject => 'Créer un projet';

  @override
  String get updateProject => 'Mettre à jour le projet';

  @override
  String get enterFixedAmountError =>
      'Veuillez entrer un montant fixe pour les projets à prix fixe';

  @override
  String get fixedAmountGreaterThanZero =>
      'Le montant fixe doit être supérieur à 0';

  @override
  String get enterHourlyRateError =>
      'Veuillez entrer un taux horaire pour les projets à taux horaire';

  @override
  String get hourlyRateGreaterThanZero =>
      'Le taux horaire doit être supérieur à 0';

  @override
  String get freelanceManagementSlogan =>
      'Solution complète de gestion freelance';

  @override
  String get appInfo => 'Informations de l\'application';

  @override
  String get endDateAfterStart =>
      'La date de fin doit être après la date de début';

  @override
  String get progressBetween0And100 =>
      'Le pourcentage d\'avancement doit être entre 0 et 100';

  @override
  String get validProgressRequired =>
      'Veuillez entrer un pourcentage d\'avancement valide';

  @override
  String get projectUpdated => 'Projet mis à jour avec succès';

  @override
  String get projectCreated => 'Projet créé avec succès';

  @override
  String projectSaveError(Object error) {
    return 'Erreur lors de l\'enregistrement du projet : $error';
  }

  @override
  String get basicInfo => 'Informations de base';

  @override
  String get pricing => 'Tarification';

  @override
  String get timeline => 'Calendrier';

  @override
  String get projectInfo => 'Informations du projet';

  @override
  String get projectDetailsHint =>
      'Saisissez les détails de base de votre projet';

  @override
  String get projectNameHint => 'Entrez un nom de projet descriptif';

  @override
  String get descriptionHint => 'Décrivez ce que ce projet implique...';

  @override
  String get tipProjectDescription =>
      'Conseil : Utilisez un nom clair et une description détaillée pour suivre l\'avancement de votre projet.';

  @override
  String get clientSelectionHint => 'Choisissez le client pour ce projet';

  @override
  String get selectClientHint => 'Sélectionnez un client pour ce projet';

  @override
  String get clientSelected => 'Client sélectionné';

  @override
  String clientCurrencyInfo(Object currency) {
    return 'La devise sera définie sur $currency';
  }

  @override
  String get addClientInfo =>
      'Vous devez ajouter au moins un client avant de créer un projet. Allez dans la gestion des clients pour en ajouter.';

  @override
  String get pricingHint =>
      'Définissez votre modèle de tarification et vos tarifs';

  @override
  String get pricingModel => 'Modèle de tarification';

  @override
  String get oneTimePayment => 'Paiement unique';

  @override
  String get payPerHour => 'Payer à l\'heure';

  @override
  String get fixedAmount => 'Montant fixe';

  @override
  String get totalProjectAmount => 'Saisissez le montant total du projet';

  @override
  String get hourlyRateHint => 'Entrez votre taux horaire';

  @override
  String get estimatedHoursHint => 'Heures estimées';

  @override
  String get actualHoursHint => 'Heures réelles';

  @override
  String get timelineHint => 'Définissez le calendrier et le statut du projet';

  @override
  String get progressHint => 'Entrez un pourcentage de progression (0-100)';

  @override
  String get startDateHint => 'Sélectionnez la date de début';

  @override
  String get endDateHint => 'Sélectionnez la date de fin';

  @override
  String get readyToCreate => 'Prêt à créer le projet';

  @override
  String get reviewCreateInfo =>
      'Vérifiez toutes les informations et cliquez sur \"Créer le projet\" pour l\'ajouter à votre portefeuille.';

  @override
  String get updating => 'Mise à jour...';

  @override
  String get creating => 'Création...';

  @override
  String errorLoadingClients(Object error) {
    return 'Erreur lors du chargement des clients : $error';
  }

  @override
  String get clientNotFoundWarning =>
      'Avertissement : le client d\'origine est introuvable. Veuillez en sélectionner un.';

  @override
  String get projectNameRequired => 'Le nom du projet est requis';

  @override
  String get descriptionRequired => 'La description est requise';

  @override
  String get hourlyRateRequired => 'Le taux horaire est requis';

  @override
  String get enterValidHourlyRate => 'Veuillez entrer un taux horaire valide';

  @override
  String get fixedAmountRequired => 'Le montant fixe est requis';

  @override
  String get enterValidAmount => 'Veuillez entrer un montant valide';

  @override
  String get enterValidHours => 'Veuillez entrer un nombre d\'heures valide';

  @override
  String get paymentFilterFullyPaid => 'Entièrement payé';

  @override
  String get paymentFilterPartiallyPaid => 'Partiellement payé';

  @override
  String get paymentFilterUnpaid => 'Non payé';

  @override
  String get paymentFilterOverdue => 'En retard';

  @override
  String get noProjectsFound => 'Aucun projet trouvé';

  @override
  String get noProjectsYet => 'Pas encore de projets';

  @override
  String get adjustSearchOrFilters =>
      'Essayez de modifier votre recherche ou vos filtres';

  @override
  String get createFirstProject => 'Créez votre premier projet pour commencer';

  @override
  String get getStarted => 'Commencer';

  @override
  String get skip => 'Passer';

  @override
  String get welcomeSubtitle =>
      'Gérez votre activité freelance en toute simplicité';
}
