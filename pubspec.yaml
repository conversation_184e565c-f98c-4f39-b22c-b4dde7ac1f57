name: freelancer_mobile
description: "FreeLancer Mobile - A beautiful animated Flutter app for freelancers."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: any

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Animation library for smooth animations
  flutter_animate: ^4.5.0

  # Beautiful icons
  font_awesome_flutter: ^10.7.0

  # Custom fonts
  google_fonts: ^6.2.1

  # SVG support
  flutter_svg: ^2.0.10+1

  # Local database with SQLite
  sqflite: ^2.3.3+1
  path: ^1.9.0

  # UUID generation for local database
  uuid: ^4.4.0

  # Crypto for password hashing
  crypto: ^3.0.3

  # Calendar widget
  table_calendar: ^3.0.9

  # Offline support and local storage
  connectivity_plus: ^6.0.3
  shared_preferences: ^2.2.3

  # JSON serialization
  json_annotation: ^4.9.0

  # PDF generation and printing
  pdf: ^3.10.7
  printing: ^5.12.0
  path_provider: ^2.1.5
  share_plus: ^7.2.2
  open_file: ^3.3.2
  image_picker: ^1.1.2
  url_launcher: ^6.3.1
  qr_flutter: ^4.1.0

  # Charts and data visualization
  fl_chart: ^0.68.0

  # Additional export functionality
  csv: ^5.1.1
  permission_handler: ^11.3.0
  file_picker: ^8.0.0+1
  device_info_plus: ^10.1.0

  # Supabase for backend services
  supabase_flutter: ^2.8.0

  # Video player for welcome screen
  video_player: ^2.8.6

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

  # Code generation for JSON serialization
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  flutter_launcher_icons: ^0.14.3

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Internationalization support
  generate: true

  # Localization configuration
  assets:
    - lib/l10n/
    - assets/images/
    - assets/videos/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/briefcase-logo.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/images/briefcase-logo.png"
  windows:
    generate: true
    image_path: "assets/images/briefcase-logo.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/images/briefcase-logo.png"
